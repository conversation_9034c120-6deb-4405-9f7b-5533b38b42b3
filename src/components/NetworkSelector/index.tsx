import {
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger,
} from "@heroui/dropdown";
import { Network } from "@/types/networks.ts";
import { Image } from "@heroui/image";

export type NetworkSelectorProps = {
  networks: Network[];
};

export function NetworkSelector(props: NetworkSelectorProps) {
  return (
    <Dropdown>
      <DropdownTrigger>
        <Button className="capitalize" variant="bordered">
          {selectedValue}
        </Button>
      </DropdownTrigger>
      <DropdownMenu
        disallowEmptySelection
        aria-label="Multiple selection example"
        closeOnSelect={false}
        selectionMode="single"
        variant="flat"
      >
        {props.networks.map((network) => (
          <DropdownItem key={network.chainId}>
            return (
            <Image
              alt={network.name}
              height={200}
              src={network.logoUrl}
              width={300}
            />
            <div>{network.name}</div>
          </DropdownItem>
        ))}
      </DropdownMenu>
    </Dropdown>
  );
}
